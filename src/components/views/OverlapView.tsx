import React, { useState, useMemo } from 'react';
import { ProcessedData, Student, ClassInfo } from '../../types';
import { normalizeName } from '../../data/nameOverrides';
import { SearchBar } from '../SearchBar';
import { FilterDropdown } from '../FilterDropdown';
import { StudentDetailModal } from '../StudentDetailModal';
import { ClassDetailModal } from '../ClassDetailModal';
import { Shuffle, Users } from 'lucide-react';

interface StudentSelectorProps {
  label: string;
  searchValue: string;
  onSearchChange: (value: string) => void;
  selectedStudent: Student | null;
  onStudentSelect: (student: Student | null) => void;
  filteredStudents: Student[];
  onStudentClick: (student: Student) => void;
}

const StudentSelector: React.FC<StudentSelectorProps> = ({
  label,
  searchValue,
  onSearchChange,
  selectedStudent,
  onStudentSelect,
  filteredStudents,
  onStudentClick,
}) => {
  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{label}</label>
      <SearchBar
        value={searchValue}
        onChange={onSearchChange}
        placeholder="Search for a student..."
      />

      {selectedStudent && (
        <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div
              onClick={() => onStudentClick(selectedStudent)}
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded p-1 -m-1 transition-colors"
            >
              <p className="font-medium text-gray-900 dark:text-white">{normalizeName(selectedStudent.name)}</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Class of {selectedStudent.gradYear}</p>
            </div>
            <button
              onClick={() => onStudentSelect(null)}
              className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 text-sm"
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {searchValue && !selectedStudent && filteredStudents.length > 0 && (
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg max-h-48 overflow-y-auto">
          {filteredStudents.map((student, index) => (
            <button
              key={index}
              onClick={() => onStudentSelect(student)}
              className="w-full text-left px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-600 last:border-b-0"
            >
              <p className="font-medium text-gray-900 dark:text-white">{normalizeName(student.name)}</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Class of {student.gradYear}</p>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

interface OverlapViewProps {
  data: ProcessedData;
}

export const OverlapView: React.FC<OverlapViewProps> = ({ data }) => {
  const [student1Search, setStudent1Search] = useState('');
  const [student2Search, setStudent2Search] = useState('');
  const [selectedStudent1, setSelectedStudent1] = useState<Student | null>(null);
  const [selectedStudent2, setSelectedStudent2] = useState<Student | null>(null);
  const [sortBy, setSortBy] = useState('name_asc');
  const [modalStudent, setModalStudent] = useState<Student | null>(null);
  const [modalClass, setModalClass] = useState<ClassInfo | null>(null);

  const filteredStudents1 = useMemo(() => {
    return data.students.filter(student =>
      normalizeName(student.name).toLowerCase().includes(student1Search.toLowerCase())
    ).slice(0, 10);
  }, [data.students, student1Search]);

  const filteredStudents2 = useMemo(() => {
    return data.students.filter(student =>
      normalizeName(student.name).toLowerCase().includes(student2Search.toLowerCase())
    ).slice(0, 10);
  }, [data.students, student2Search]);

  const commonClasses = useMemo(() => {
    if (!selectedStudent1 || !selectedStudent2) return [];

  const student1Key = selectedStudent1.email || normalizeName(selectedStudent1.name);
  const student2Key = selectedStudent2.email || normalizeName(selectedStudent2.name);

    const student1Classes = data.studentClassMap.get(student1Key) || [];
    const student2Classes = data.studentClassMap.get(student2Key) || [];

    return student1Classes.filter(cls1 =>
      student2Classes.some(cls2 =>
        cls1.name === cls2.name && cls1.block === cls2.block
      )
    );
  }, [selectedStudent1, selectedStudent2, data.studentClassMap]);

  const sortOptions = [
    { value: 'name_asc', label: 'Name (A → Z)' },
    { value: 'name_desc', label: 'Name (Z → A)' },
    { value: 'block_asc', label: 'Block (asc)' },
    { value: 'block_desc', label: 'Block (desc)' },
  ];

  const sortedCommonClasses = useMemo(() => {
    const list = [...commonClasses];
    switch (sortBy) {
      case 'name_desc':
        return list.sort((a, b) => b.name.localeCompare(a.name));
      case 'block_asc':
        return list.sort((a, b) => a.block.localeCompare(b.block));
      case 'block_desc':
        return list.sort((a, b) => b.block.localeCompare(a.block));
      case 'name_asc':
      default:
        return list.sort((a, b) => a.name.localeCompare(b.name));
    }
  }, [commonClasses, sortBy]);


  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Class Overlap Finder</h2>
        <p className="text-gray-600 dark:text-gray-400">Find classes that two students have in common</p>
      </div>

      <div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <StudentSelector
            label="First Student"
            searchValue={student1Search}
            onSearchChange={(val) => {
              // typing should clear a previously selected student so suggestions reappear
              if (selectedStudent1 && val !== normalizeName(selectedStudent1.name)) {
                setSelectedStudent1(null);
              }
              setStudent1Search(val);
            }}
            selectedStudent={selectedStudent1}
            onStudentSelect={(stu) => {
              // when selecting from suggestions, set both the selected student and the search text
              setSelectedStudent1(stu);
              setStudent1Search(stu ? normalizeName(stu.name) : '');
            }}
            filteredStudents={filteredStudents1}
            onStudentClick={setModalStudent}
          />

          <StudentSelector
            label="Second Student"
            searchValue={student2Search}
            onSearchChange={(val) => {
              if (selectedStudent2 && val !== normalizeName(selectedStudent2.name)) {
                setSelectedStudent2(null);
              }
              setStudent2Search(val);
            }}
            selectedStudent={selectedStudent2}
            onStudentSelect={(stu) => {
              setSelectedStudent2(stu);
              setStudent2Search(stu ? normalizeName(stu.name) : '');
            }}
            filteredStudents={filteredStudents2}
            onStudentClick={setModalStudent}
          />
        </div>

        <div className="mt-3 flex justify-end">
          <div className="w-full lg:w-48">
            <FilterDropdown
              label="Sort"
              value={sortBy}
              onChange={setSortBy}
              options={sortOptions}
            />
          </div>
        </div>
      </div>

      {selectedStudent1 && selectedStudent2 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center gap-2 mb-4">
            <Shuffle className="w-5 h-5 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Common Classes
            </h3>
          </div>

          {sortedCommonClasses.length > 0 ? (
            <div className="space-y-4">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {normalizeName(selectedStudent1.name)} and {normalizeName(selectedStudent2.name)} share {commonClasses.length} class{commonClasses.length !== 1 ? 'es' : ''}:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {sortedCommonClasses.map((cls, index) => (
                  <div
                    key={index}
                    onClick={() => setModalClass(cls)}
                    className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer"
                  >
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-1">{cls.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Teacher: {cls.teacher.name}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Block: {cls.block}</p>
                    <div className="mt-2">
                      <p className="text-xs text-gray-500 dark:text-gray-400">{cls.students.length} total students</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Users className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 dark:text-gray-400">
                {normalizeName(selectedStudent1.name)} and {normalizeName(selectedStudent2.name)} don't share any classes.
              </p>
            </div>
          )}
        </div>
      )}

      {!selectedStudent1 || !selectedStudent2 ? (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <Shuffle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">
            Select two students to find their common classes
          </p>
        </div>
      ) : null}

      {/* Modals */}
      {modalStudent && (
        <StudentDetailModal
          student={modalStudent}
          classes={data.studentClassMap.get(modalStudent.email || normalizeName(modalStudent.name)) || []}
          data={data}
          onClose={() => setModalStudent(null)}
        />
      )}

      {modalClass && (
        <ClassDetailModal
          classInfo={modalClass}
          data={data}
          onClose={() => setModalClass(null)}
        />
      )}
    </div>
  );
};
