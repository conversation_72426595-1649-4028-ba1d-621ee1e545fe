import React from 'react';
import { ProcessedData } from '../../types';
import { Users, GraduationCap, BookOpen, TrendingUp } from 'lucide-react';
import { useSettings } from '../../contexts/SettingsContext';

interface OverviewViewProps {
  data: ProcessedData;
}

export const OverviewView: React.FC<OverviewViewProps> = ({ data }) => {
  const { settings } = useSettings();

  const gradYearStats = data.students.reduce((acc, student) => {
    acc[student.gradYear] = (acc[student.gradYear] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const classesInBounds = data.classes.filter(cls => cls.students.length >= settings.avgBoundsMin && cls.students.length <= settings.avgBoundsMax);
  const totalEnrollments = classesInBounds.reduce((sum, cls) => sum + cls.students.length, 0);
  const avgClassSize = classesInBounds.length > 0 ? (totalEnrollments / classesInBounds.length) : NaN;

  const statsCards = [
    {
      title: 'Total Classes',
      value: data.classes.length,
      icon: BookOpen,
      color: 'blue',
    },
    {
      title: 'Total Students',
      value: data.students.length,
      icon: GraduationCap,
      color: 'purple',
    },
    {
      title: 'Faculty Members',
      value: data.faculty.length,
      icon: Users,
      color: 'green',
    },
    {
      title: 'Avg Class Size',
      value: avgClassSize,
      icon: TrendingUp,
      color: 'orange',
    },
  ];

  // Additional detailed stats

  // Block distribution: flat by block only
  const blockDistribution = data.classes.reduce((acc, cls) => {
    acc[cls.block] = (acc[cls.block] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const avgClassesPerStudent = data.students.length > 0
    ? (Array.from(data.studentClassMap.values()).reduce((sum, arr) => sum + arr.length, 0) / data.students.length)
    : 0;

  // grad-year edge counts: missing grad year, grad year marked as '??' or 'MS'
  const noGradYearCount = data.students.filter(s => !s.gradYear || String(s.gradYear).trim() === '').length;
  const gradYearQuestionCount = data.students.filter(s => String(s.gradYear || '').includes('??')).length;
  const gradYearMSCount = data.students.filter(s => String(s.gradYear || '').toLowerCase().includes('ms')).length;

  // class size histogram buckets (computed)
  const classSizeBuckets = data.classes.reduce((acc, cls) => {
    const size = cls.students.length;
    if (size <= 5) acc['1-5'] = (acc['1-5'] || 0) + 1;
    else if (size <= 10) acc['6-10'] = (acc['6-10'] || 0) + 1;
    else if (size <= 15) acc['11-15'] = (acc['11-15'] || 0) + 1;
    else if (size <= 20) acc['16-20'] = (acc['16-20'] || 0) + 1;
    else acc['21+'] = (acc['21+'] || 0) + 1;
    return acc;
  }, { '21+': 0, '16-20': 0, '11-15': 0, '6-10': 0, '1-5': 0 } as Record<string, number>);

  // totals and derived counts for grad-year edge metrics
  const totalStudents = data.students.length;
  const haveQ = gradYearQuestionCount; // students whose gradYear contains '??'
  const haveMS = gradYearMSCount; // students whose gradYear contains 'MS'
  const unionCount = data.students.filter(s => String(s.gradYear || '').includes('??') || String(s.gradYear || '').toLowerCase().includes('ms')).length;
  const withoutQ = totalStudents - haveQ;
  const withoutMS = totalStudents - haveMS;
  const withoutEither = totalStudents - unionCount;





  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {statsCards.map((stat) => {
          const Icon = stat.icon;
          const colorClasses = {
            blue: 'bg-blue-500 text-white',
            purple: 'bg-purple-500 text-white',
            green: 'bg-green-500 text-white',
            orange: 'bg-orange-500 text-white',
          };

          return (
                  <div key={stat.title} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.title}</p>
                        <p className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                          {stat.title === 'Avg Class Size' ? (
                            classesInBounds.length > 0 ? (
                              <>
                                {avgClassSize.toFixed(2)}
                                <span className="text-sm font-normal text-gray-600 block">(calculated from {classesInBounds.length} classes)</span>
                              </>
                            ) : (
                              'N/A'
                            )
                          ) : (
                            stat.value
                          )}
                        </p>
                      </div>
                      <div className={`p-3 rounded-lg ${colorClasses[stat.color as keyof typeof colorClasses]}`}>
                        <Icon className="w-6 h-6" />
                      </div>
                    </div>
                  </div>
                );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Students by Graduation Year
          </h3>
          <div className="space-y-3">
            {Object.entries(gradYearStats)
              .sort(([a], [b]) => a.localeCompare(b))
              .map(([year, count]) => (
                <div key={year} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Class of {year}
                  </span>
                  <div className="flex items-center gap-2">
                    <div
                      className="bg-blue-200 h-2 rounded-full"
                      style={{
                        width: `${(count / Math.max(...Object.values(gradYearStats))) * 100}px`,
                        minWidth: '20px'
                      }}
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Largest Classes</h3>
            <div>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Avg bounds: {settings.avgBoundsMin}–{settings.avgBoundsMax}
              </span>
            </div>
          </div>
          <div className="space-y-3">
            {data.classes
              .sort((a, b) => b.students.length - a.students.length)
              .slice(0, 8)
              .map((cls, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{cls.name}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{cls.teacher.name}</p>
                  </div>
                  <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    {cls.students.length} students
                  </span>
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* Move advanced details section to the bottom and match card styling */}
      {settings.showAdvancedStats && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Detailed Stats & Insights</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">A compact dashboard with deeper metrics: top teachers by total students, most popular classes, block distribution, average classes per student and class-size distribution.</p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-3">Top Teachers (by students)</h4>
              <div className="space-y-4">
                {(() => {
                  // compute teacher -> total students across their classes
                  const teacherCounts: Array<{ name: string; classes: number; students: number }> = Array.from((data.teacherClassMap || new Map()).entries()).map(([tKey, classes]) => ({
                    name: classes[0]?.teacher?.name || String(tKey),
                    classes: classes.length,
                    students: classes.reduce((s: number, c: any) => s + (c.students?.length || 0), 0),
                  }));

                  teacherCounts.sort((a, b) => b.students - a.students);
                  return (
                    <div className="space-y-3">
                      {teacherCounts.slice(0, 8).map((t) => (
                        <div key={t.name}>
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{t.name}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">{t.classes} classes • {t.students} students</div>
                        </div>
                      ))}
                    </div>
                  );
                })()}

                <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mt-4">Most Popular Classes</h4>
                <div className="space-y-2 mt-2">
                  {data.classes
                    .slice()
                    .sort((a, b) => (b.students.length || 0) - (a.students.length || 0))
                    .slice(0, 12)
                    .map((cls, idx) => (
                      <div key={idx} className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{cls.name}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">{cls.teacher?.name || 'Unknown Teacher'}</p>
                        </div>
                        <span className="text-sm font-medium text-blue-600 dark:text-blue-400">{cls.students.length}</span>
                      </div>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-3">Class size buckets</h4>
              <div className="space-y-3">
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">21+</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{classSizeBuckets['21+'] ?? 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">16-20</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{classSizeBuckets['16-20'] ?? 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">11-15</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{classSizeBuckets['11-15'] ?? 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">6-10</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{classSizeBuckets['6-10'] ?? 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">1-5</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{classSizeBuckets['1-5'] ?? 0}</span>
                  </div>
                </div>

                <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mt-4">Totals</h4>
                <div className="space-y-1 text-sm text-gray-700 dark:text-gray-300">
                  <div className="flex items-center justify-between">
                    <span>Total students</span>
                    <span className="text-gray-600 dark:text-gray-400">{totalStudents}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Students without '??'</span>
                    <span className="text-gray-600 dark:text-gray-400">{withoutQ}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Students without 'MS'</span>
                    <span className="text-gray-600 dark:text-gray-400">{withoutMS}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Students without either</span>
                    <span className="text-gray-600 dark:text-gray-400">{withoutEither}</span>
                  </div>
                </div>

                <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mt-4">Grad-year edge cases</h4>
                <div className="mt-2 space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">No grad year</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{noGradYearCount}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">Grad year '??'</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{gradYearQuestionCount}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700 dark:text-gray-300">Grad year 'MS'</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{gradYearMSCount}</span>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-3">Distributions & Averages</h4>
              <div className="space-y-3">
                <div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Avg classes per student</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{avgClassesPerStudent.toFixed(2)}</p>
                </div>

                <div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Block distribution</p>
                  <div className="mt-2 space-y-1">
                    {Object.entries(blockDistribution).sort(([a],[b]) => a.localeCompare(b)).map(([block, count]) => (
                      <div key={block} className="flex items-center justify-between">
                        <span className="text-sm text-gray-700 dark:text-gray-300">Block {block}</span>
                        <span className="text-sm text-gray-600 dark:text-gray-400">{count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
