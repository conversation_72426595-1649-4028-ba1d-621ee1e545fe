import React, { useState } from 'react';
import { ClassInfo, Student, ProcessedData } from '../types';
import { X, Users, Clock, Mail } from 'lucide-react';
import { normalizeName } from '../data/nameOverrides';
import { StudentDetailModal } from './StudentDetailModal';

interface ClassDetailModalProps {
  classInfo: ClassInfo;
  data: ProcessedData;
  onClose: () => void;
}

export const ClassDetailModal: React.FC<ClassDetailModalProps> = ({ classInfo, data, onClose }) => {
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  return (
    <div className="modal-backdrop fixed inset-0 bg-black/50 dark:bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{classInfo.name}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">Teacher: {classInfo.teacher.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">Block: {classInfo.block}</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">{classInfo.students.length} Students</span>
              </div>
            </div>

            {classInfo.teacher.email && (
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-500 dark:text-gray-400">{classInfo.teacher.email}</span>
              </div>
            )}
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Student Roster</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {classInfo.students
                .sort((a, b) => normalizeName(a.name).localeCompare(normalizeName(b.name)))
                .map((student, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/30 rounded-lg cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-800/50 transition-colors border border-purple-200 dark:border-purple-700"
                    onClick={() => setSelectedStudent(student)}
                  >
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{normalizeName(student.name)}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Class of {student.gradYear}</p>
                    </div>
                    {student.email && (
                      <Mail className="w-4 h-4 text-gray-400" />
                    )}
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>

      {selectedStudent && (
        <StudentDetailModal
          student={selectedStudent}
          classes={data.studentClassMap.get(selectedStudent.email || normalizeName(selectedStudent.name)) || []}
          data={data}
          onClose={() => setSelectedStudent(null)}
        />
      )}
    </div>
  );
};
